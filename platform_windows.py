#!/usr/bin/env python3
"""
Windows-specific implementations for Subtitle Reader application.
"""

import sys
import logging
import subprocess
import hashlib
import locale
from typing import Optional, Dict, List, Any
from pathlib import Path

from platform_utils import (
    TTSProvider, ActiveAppDetector, SystemInfoProvider, 
    BrowserURLDetector, PlatformError, IS_WINDOWS
)

if not IS_WINDOWS:
    raise ImportError("This module is only for Windows platform")

# Windows-specific imports
try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logging.warning("⚠️ pyttsx3 not available - TTS will not work")

try:
    import win32gui
    import win32process
    import win32api
    import win32con
    PYWIN32_AVAILABLE = True
except ImportError:
    PYWIN32_AVAILABLE = False
    logging.warning("⚠️ pywin32 not available - some features will not work")

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    logging.warning("⚠️ WMI not available - hardware fingerprinting may be limited")

class WindowsTTSProvider(TTSProvider):
    """Windows TTS implementation using pyttsx3."""
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.current_process = None
        self._initialize_engine()
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS and PYTTSX3_AVAILABLE
    
    def _initialize_engine(self):
        """Initialize pyttsx3 engine."""
        if not PYTTSX3_AVAILABLE:
            logging.error("❌ pyttsx3 not available")
            return
        
        try:
            self.engine = pyttsx3.init()
            # Set default properties
            self.engine.setProperty('rate', 200)
            self.engine.setProperty('volume', 1.0)
            logging.info("✅ Windows TTS engine initialized")
        except Exception as e:
            logging.error(f"❌ Failed to initialize TTS engine: {e}")
            self.engine = None
    
    def speak(self, text: str, voice: str = None, rate: int = 200) -> bool:
        """Speak text using Windows TTS."""
        if not self.engine:
            logging.error("❌ TTS engine not available")
            return False
        
        try:
            # Set rate
            self.engine.setProperty('rate', rate)
            
            # Set voice if specified
            if voice:
                voices = self.engine.getProperty('voices')
                for v in voices:
                    if voice.lower() in v.name.lower():
                        self.engine.setProperty('voice', v.id)
                        break
            
            # Speak text
            self.engine.say(text)
            self.engine.runAndWait()
            return True
            
        except Exception as e:
            logging.error(f"❌ TTS speak error: {e}")
            return False
    
    def stop(self) -> bool:
        """Stop current TTS playback."""
        if not self.engine:
            return False
        
        try:
            self.engine.stop()
            return True
        except Exception as e:
            logging.error(f"❌ TTS stop error: {e}")
            return False
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available Windows TTS voices."""
        if not self.engine:
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for voice in voices:
                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'language': getattr(voice, 'languages', ['en-US'])[0] if hasattr(voice, 'languages') else 'en-US',
                    'gender': getattr(voice, 'gender', 'unknown'),
                    'quality': 'standard'
                }
                voice_list.append(voice_info)
            
            logging.info(f"🎤 Found {len(voice_list)} Windows TTS voices")
            return voice_list
            
        except Exception as e:
            logging.error(f"❌ Error getting voices: {e}")
            return []
    
    def is_speaking(self) -> bool:
        """Check if TTS is currently speaking."""
        if not self.engine:
            return False
        
        try:
            return self.engine.isBusy()
        except:
            return False

class WindowsActiveAppDetector(ActiveAppDetector):
    """Windows active application detection using win32gui."""
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS and PYWIN32_AVAILABLE
    
    def get_active_app_name(self) -> str:
        """Get name of currently active application on Windows."""
        if not PYWIN32_AVAILABLE:
            logging.warning("⚠️ pywin32 not available for active app detection")
            return ""
        
        try:
            # Get foreground window
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                return ""
            
            # Get process ID
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            
            # Get process handle
            handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, False, pid)
            
            # Get executable name
            exe_name = win32process.GetModuleFileNameEx(handle, 0)
            app_name = Path(exe_name).stem
            
            win32api.CloseHandle(handle)
            return app_name
            
        except Exception as e:
            logging.debug(f"⚠️ Error getting active app: {e}")
            return ""
    
    def get_active_window_title(self) -> str:
        """Get title of currently active window."""
        if not PYWIN32_AVAILABLE:
            return ""
        
        try:
            hwnd = win32gui.GetForegroundWindow()
            if hwnd:
                return win32gui.GetWindowText(hwnd)
        except Exception as e:
            logging.debug(f"⚠️ Error getting window title: {e}")
        
        return ""

class WindowsSystemInfoProvider(SystemInfoProvider):
    """Windows system information provider."""
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS
    
    def get_system_language(self) -> str:
        """Get Windows system default language."""
        try:
            # Try to get system locale
            lang_code = locale.getdefaultlocale()[0]
            if lang_code:
                # Convert from locale format (e.g., 'en_US') to standard format
                if '_' in lang_code:
                    lang, region = lang_code.split('_', 1)
                    return f"{lang}-{region}"
                return lang_code
        except Exception as e:
            logging.debug(f"⚠️ Error getting system language: {e}")
        
        # Fallback to English
        return "en-US"
    
    def get_hardware_fingerprint(self) -> str:
        """Get Windows hardware fingerprint."""
        components = []
        
        try:
            # Try WMI first
            if WMI_AVAILABLE:
                c = wmi.WMI()
                
                # Get motherboard info
                for board in c.Win32_BaseBoard():
                    if board.SerialNumber:
                        components.append(f"mb:{board.SerialNumber}")
                
                # Get CPU info
                for cpu in c.Win32_Processor():
                    if cpu.ProcessorId:
                        components.append(f"cpu:{cpu.ProcessorId}")
                
                # Get BIOS info
                for bios in c.Win32_BIOS():
                    if bios.SerialNumber:
                        components.append(f"bios:{bios.SerialNumber}")
            
            # Fallback methods
            if not components:
                # Use computer name as fallback
                import socket
                hostname = socket.gethostname()
                components.append(f"hostname:{hostname}")
                
                # Try to get volume serial number
                try:
                    import win32api
                    volume_info = win32api.GetVolumeInformation("C:\\")
                    if volume_info[1]:  # Serial number
                        components.append(f"volume:{volume_info[1]}")
                except:
                    pass
            
            if not components:
                # Ultimate fallback
                import platform
                components.append(f"fallback:{platform.node()}")
            
            # Create hash
            combined = "|".join(sorted(components))
            fingerprint = hashlib.sha256(combined.encode('utf-8')).hexdigest()[:32]
            
            logging.info(f"🔐 Windows hardware fingerprint created: {fingerprint[:8]}...")
            return fingerprint
            
        except Exception as e:
            logging.error(f"❌ Error creating hardware fingerprint: {e}")
            # Emergency fallback
            import platform
            fallback = hashlib.sha256(platform.node().encode('utf-8')).hexdigest()[:32]
            return fallback

class WindowsBrowserURLDetector(BrowserURLDetector):
    """Windows browser URL detection (limited functionality)."""
    
    def _check_platform_support(self) -> bool:
        return IS_WINDOWS
    
    def get_browser_url(self) -> str:
        """Get current browser URL (Windows implementation limited)."""
        logging.warning("⚠️ Browser URL detection not fully implemented for Windows")
        # TODO: Implement using UI Automation or browser-specific APIs
        return ""
    
    def get_youtube_video_time(self, url: str) -> Optional[float]:
        """Get current YouTube video time (Windows implementation limited)."""
        logging.warning("⚠️ YouTube video time detection not implemented for Windows")
        # TODO: Implement using browser automation
        return None
