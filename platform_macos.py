#!/usr/bin/env python3
"""
macOS-specific implementations for Subtitle Reader application.
"""

import sys
import logging
import subprocess
import hashlib
from typing import Optional, Dict, List, Any
from pathlib import Path

from platform_utils import (
    TTSProvider, ActiveAppDetector, SystemInfoProvider, 
    BrowserURLDetector, PlatformError, IS_MACOS
)

if not IS_MACOS:
    raise ImportError("This module is only for macOS platform")

# macOS-specific imports
try:
    import AppKit
    APPKIT_AVAILABLE = True
except ImportError:
    APPKIT_AVAILABLE = False
    logging.warning("⚠️ AppKit not available - some features will not work")

class MacOSTTSProvider(TTSProvider):
    """macOS TTS implementation using 'say' command."""
    
    def __init__(self):
        super().__init__()
        self.current_process = None
    
    def _check_platform_support(self) -> bool:
        return IS_MACOS
    
    def speak(self, text: str, voice: str = None, rate: int = 200) -> bool:
        """Speak text using macOS 'say' command."""
        try:
            # Build command
            cmd_list = ['/usr/bin/say']
            
            if voice:
                # Normalize voice name for macOS
                normalized_voice = self._normalize_voice_name(voice)
                cmd_list.extend(['-v', normalized_voice])
            
            cmd_list.extend(['-r', str(rate), '--', text])
            
            # Execute command
            self.current_process = subprocess.Popen(
                cmd_list, 
                stdout=subprocess.DEVNULL, 
                stderr=subprocess.PIPE
            )
            
            # Check for errors
            stderr_output = self.current_process.communicate()[1].decode().strip()
            if stderr_output:
                logging.error(f"❌ macOS TTS error: {stderr_output}")
                # Fallback without voice
                if 'not find' in stderr_output.lower():
                    cmd_fallback = ['/usr/bin/say', '-r', str(rate), '--', text]
                    self.current_process = subprocess.Popen(
                        cmd_fallback, 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.PIPE
                    )
            
            return True
            
        except Exception as e:
            logging.error(f"❌ macOS TTS speak error: {e}")
            return False
    
    def stop(self) -> bool:
        """Stop current TTS playback."""
        try:
            if self.current_process and self.current_process.poll() is None:
                self.current_process.terminate()
                self.current_process.wait(timeout=1.0)
            return True
        except Exception as e:
            logging.error(f"❌ macOS TTS stop error: {e}")
            return False
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get list of available macOS TTS voices."""
        voices = []
        
        try:
            # Try NSSpeechSynthesizer first
            if APPKIT_AVAILABLE:
                voices = self._get_voices_via_appkit()
            
            # Fallback to 'say -v ?' command
            if not voices:
                voices = self._get_voices_via_say_command()
                
        except Exception as e:
            logging.error(f"❌ Error getting macOS voices: {e}")
        
        return voices
    
    def _get_voices_via_appkit(self) -> List[Dict[str, Any]]:
        """Get voices using AppKit NSSpeechSynthesizer."""
        voices = []
        
        try:
            voice_ids = AppKit.NSSpeechSynthesizer.availableVoices()
            
            for voice_id in voice_ids:
                attrs = AppKit.NSSpeechSynthesizer.attributesForVoice_(voice_id)
                voice_name = attrs.get('VoiceName', 'Unknown')
                locale_id = attrs.get('VoiceLocaleIdentifier', 'en-US')
                
                # Determine quality
                quality = 'standard'
                if '(Enhanced)' in voice_name or '(Premium)' in voice_name:
                    if '(Premium)' in voice_name:
                        quality = 'premium'
                    else:
                        quality = 'enhanced'
                
                voice_info = {
                    'id': str(voice_id),
                    'name': voice_name,
                    'language': locale_id,
                    'quality': quality
                }
                voices.append(voice_info)
                
        except Exception as e:
            logging.error(f"❌ AppKit voice detection error: {e}")
        
        return voices
    
    def _get_voices_via_say_command(self) -> List[Dict[str, Any]]:
        """Get voices using 'say -v ?' command."""
        voices = []
        
        try:
            result = subprocess.run(
                ["say", "-v", "?"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        # Parse voice line: "Alex en_US # Most people recognize me by my voice."
                        parts = line.split('#')[0].strip().split()
                        if len(parts) >= 2:
                            voice_name = parts[0]
                            locale = parts[1].replace('_', '-')
                            
                            voice_info = {
                                'id': voice_name,
                                'name': voice_name,
                                'language': locale,
                                'quality': 'standard'
                            }
                            voices.append(voice_info)
                            
        except Exception as e:
            logging.error(f"❌ Say command voice detection error: {e}")
        
        return voices
    
    def _normalize_voice_name(self, voice: str) -> str:
        """Normalize voice name for macOS say command."""
        if not voice:
            return 'Samantha'
        
        # Handle quality indicators
        normalized = voice.strip()
        normalized = normalized.replace('(vylepšený)', '(Enhanced)')
        normalized = normalized.replace('(prémiový)', '(Premium)')
        normalized = normalized.replace('(enhanced)', '(Enhanced)')
        normalized = normalized.replace('(premium)', '(Premium)')
        
        return normalized
    
    def is_speaking(self) -> bool:
        """Check if TTS is currently speaking."""
        return (self.current_process is not None and 
                self.current_process.poll() is None)

class MacOSActiveAppDetector(ActiveAppDetector):
    """macOS active application detection using AppKit."""
    
    def _check_platform_support(self) -> bool:
        return IS_MACOS and APPKIT_AVAILABLE
    
    def get_active_app_name(self) -> str:
        """Get name of currently active application on macOS."""
        if not APPKIT_AVAILABLE:
            logging.warning("⚠️ AppKit not available for active app detection")
            return ""
        
        try:
            active_app = AppKit.NSWorkspace.sharedWorkspace().frontmostApplication()
            if active_app:
                app_name = active_app.localizedName()
                return app_name if app_name else ""
        except Exception as e:
            logging.debug(f"⚠️ Error getting active app: {e}")
        
        return ""
    
    def get_active_window_title(self) -> str:
        """Get title of currently active window."""
        # macOS doesn't easily provide window titles without accessibility permissions
        # This would require additional implementation
        return ""

class MacOSSystemInfoProvider(SystemInfoProvider):
    """macOS system information provider."""
    
    def _check_platform_support(self) -> bool:
        return IS_MACOS
    
    def get_system_language(self) -> str:
        """Get macOS system default language."""
        try:
            result = subprocess.run(
                ['defaults', 'read', '-g', 'AppleLanguages'],
                capture_output=True, text=True, timeout=5
            )
            
            if result.returncode == 0:
                output = result.stdout.strip()
                if '(' in output and '"' in output:
                    languages = output.split('"')
                    if len(languages) >= 2:
                        return languages[1]  # First language
                        
        except Exception as e:
            logging.debug(f"⚠️ Error getting system language: {e}")
        
        # Fallback
        return "en-US"
    
    def get_hardware_fingerprint(self) -> str:
        """Get macOS hardware fingerprint."""
        components = []
        
        try:
            # Get system serial number
            serial = self._get_system_serial()
            if serial:
                components.append(f"serial:{serial}")
            
            # Get hardware UUID
            uuid = self._get_hardware_uuid()
            if uuid:
                components.append(f"uuid:{uuid}")
            
            # Get MAC address
            mac = self._get_primary_mac_address()
            if mac:
                components.append(f"mac:{mac}")
            
            # Get motherboard info
            mb_info = self._get_motherboard_info()
            if mb_info:
                components.append(f"mb:{mb_info}")
            
            if not components:
                # Fallback
                import platform
                components.append(f"fallback:{platform.node()}")
            
            # Create hash
            combined = "|".join(sorted(components))
            fingerprint = hashlib.sha256(combined.encode('utf-8')).hexdigest()[:32]
            
            logging.info(f"🔐 macOS hardware fingerprint created: {fingerprint[:8]}...")
            return fingerprint
            
        except Exception as e:
            logging.error(f"❌ Error creating hardware fingerprint: {e}")
            # Emergency fallback
            import platform
            fallback = hashlib.sha256(platform.node().encode('utf-8')).hexdigest()[:32]
            return fallback
    
    def _get_system_serial(self) -> Optional[str]:
        """Get macOS system serial number."""
        try:
            result = subprocess.run(
                ["system_profiler", "SPHardwareDataType"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Serial Number' in line:
                        serial = line.split(':')[-1].strip()
                        if serial and serial != "(system)":
                            return serial
        except Exception:
            pass
        
        return None
    
    def _get_hardware_uuid(self) -> Optional[str]:
        """Get macOS hardware UUID."""
        try:
            result = subprocess.run(
                ["system_profiler", "SPHardwareDataType"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Hardware UUID' in line:
                        uuid = line.split(':')[-1].strip()
                        if uuid:
                            return uuid
        except Exception:
            pass
        
        return None
    
    def _get_primary_mac_address(self) -> Optional[str]:
        """Get primary MAC address."""
        try:
            result = subprocess.run(
                ["networksetup", "-listallhardwareports"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for i, line in enumerate(lines):
                    if 'Wi-Fi' in line or 'Ethernet' in line:
                        for j in range(i+1, min(i+4, len(lines))):
                            if 'Ethernet Address' in lines[j]:
                                mac = lines[j].split(':')[-1].strip()
                                if mac and mac != "N/A":
                                    return mac.replace(':', '').lower()
        except Exception:
            pass
        
        return None
    
    def _get_motherboard_info(self) -> Optional[str]:
        """Get motherboard information."""
        try:
            result = subprocess.run(
                ["system_profiler", "SPHardwareDataType"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                model_info = []
                for line in result.stdout.split('\n'):
                    if 'Model Name' in line:
                        model = line.split(':')[-1].strip()
                        if model:
                            model_info.append(model)
                    elif 'Model Identifier' in line:
                        identifier = line.split(':')[-1].strip()
                        if identifier:
                            model_info.append(identifier)
                
                if model_info:
                    return "_".join(model_info)
        except Exception:
            pass
        
        return None

class MacOSBrowserURLDetector(BrowserURLDetector):
    """macOS browser URL detection using AppleScript."""
    
    def _check_platform_support(self) -> bool:
        return IS_MACOS
    
    def get_browser_url(self) -> str:
        """Get current browser URL using AppleScript."""
        # Try Safari first
        url = self._get_safari_url()
        if not url:
            # Try Chrome
            url = self._get_chrome_url()
        return url
    
    def _run_applescript(self, script: str) -> str:
        """Run AppleScript and return output."""
        try:
            process = subprocess.run(
                ['/usr/bin/osascript', '-e', script],
                capture_output=True, text=True, check=False, timeout=3
            )
            if process.returncode == 0:
                return process.stdout.strip()
        except Exception as e:
            logging.debug(f"⚠️ AppleScript error: {e}")
        return ""
    
    def _get_safari_url(self) -> str:
        """Get Safari current URL."""
        script = '''
        tell application "Safari"
            try
                if (count of windows) > 0 then
                    if exists (current tab of window 1) then
                        return URL of current tab of window 1
                    end if
                end if
            end try
            return ""
        end tell
        '''
        return self._run_applescript(script)
    
    def _get_chrome_url(self) -> str:
        """Get Chrome current URL."""
        script = '''
        tell application "Google Chrome"
            try
                if (count of windows) > 0 then
                    if exists (active tab of window 1) then
                        return URL of active tab of window 1
                    end if
                end if
            end try
            return ""
        end tell
        '''
        return self._run_applescript(script)
    
    def get_youtube_video_time(self, url: str) -> Optional[float]:
        """Get current YouTube video time using AppleScript."""
        # Try Chrome first
        time_str = self._get_youtube_time_chrome()
        if time_str:
            try:
                return float(time_str)
            except ValueError:
                pass
        
        # Try Safari
        time_str = self._get_youtube_time_safari()
        if time_str:
            try:
                return float(time_str)
            except ValueError:
                pass
        
        return None
    
    def _get_youtube_time_chrome(self) -> str:
        """Get YouTube video time from Chrome."""
        script = '''
        tell application "Google Chrome"
            try
                tell active tab of front window
                    if URL contains "youtube.com/watch" or URL contains "youtu.be/" then
                        execute javascript "document.getElementsByTagName('video')[0].currentTime"
                    else
                        return ""
                    end if
                end tell
            on error
                return ""
            end try
        end tell
        '''
        return self._run_applescript(script)
    
    def _get_youtube_time_safari(self) -> str:
        """Get YouTube video time from Safari."""
        script = '''
        tell application "Safari"
            try
                tell current tab of front window
                    if URL contains "youtube.com/watch" or URL contains "youtu.be/" then
                        execute javascript "document.getElementsByTagName('video')[0].currentTime"
                    else
                        return ""
                    end if
                end tell
            on error
                return ""
            end try
        end tell
        '''
        return self._run_applescript(script)
