# -*- coding: utf-8 -*-
"""
Demo timer pre Subtitle Reader aplikáciu
Spravuje časovač demo režimu a UI aktualizácie
"""

import time
import logging
import threading
from typing import Optional, Callable
from PyQt6 import QtCore, QtWidgets

from license_manager import get_license_manager


class DemoTimer(QtCore.QObject):
    """Qt timer pre demo režim s signálmi."""
    
    # Qt signály
    time_updated = QtCore.pyqtSignal(int)  # Zostávajúci čas v sekundách
    time_expired = QtCore.pyqtSignal()     # Demo čas vypršal
    license_changed = QtCore.pyqtSignal()  # Licencia sa zmenila
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = get_license_manager()
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_time)
        self.timer.setInterval(1000)  # <PERSON><PERSON><PERSON><PERSON> sekundu
        self.is_running = False
        self.last_license_status = None
        
    def start_timer(self):
        """Spustí demo timer."""
        if not self.is_running:
            self.is_running = True
            self.timer.start()
            logging.info("⏱️ Demo timer spustený")
    
    def stop_timer(self):
        """Zastaví demo timer."""
        if self.is_running:
            self.is_running = False
            self.timer.stop()
            logging.info("⏹️ Demo timer zastavený")
    
    def update_time(self):
        """Aktualizuje čas a emituje signály."""
        try:
            status = self.license_manager.get_license_status()
            
            # Kontrola zmeny licencie
            if status != self.last_license_status:
                self.license_changed.emit()
                self.last_license_status = status.copy()
            
            if status["type"] == "demo":
                remaining_seconds = status["remaining_seconds"]
                
                # Ak beží demo session, odpočítaj aktuálny čas
                if self.license_manager.is_demo_active:
                    current_session_time = self.license_manager.get_current_demo_time()
                    remaining_seconds = max(0, remaining_seconds - current_session_time)
                
                self.time_updated.emit(remaining_seconds)
                
                # Ak čas vypršal
                if remaining_seconds <= 0:
                    self.time_expired.emit()
                    self.stop_timer()
            else:
                # Full licencia - emituj -1 ako indikátor neobmedzeného času
                self.time_updated.emit(-1)
                
        except Exception as e:
            logging.error(f"❌ Chyba v demo timer: {e}")


class DemoWidget(QtWidgets.QWidget):
    """Widget pre zobrazenie demo informácií."""

    # 🔧 OPRAVA: Pridaj signály pre thread-safe zobrazenie dialógov
    show_demo_expired_dialog = QtCore.pyqtSignal(str, str)  # title, text
    show_error_dialog = QtCore.pyqtSignal(str, str)  # title, text

    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = get_license_manager()
        self.demo_timer = DemoTimer(self)
        self.setup_ui()
        self.connect_signals()
        self.update_display()

        # Spusti timer
        self.demo_timer.start_timer()
    
    def setup_ui(self):
        """Nastaví UI komponenty."""
        # Transparentné pozadie - použije pozadie aplikácie
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
                border: none;
                margin: 2px;
            }
        """)

        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(8)

        # Hlavný riadok s informáciami
        main_row = QtWidgets.QHBoxLayout()

        # Status text (ľavá strana)
        self.status_label = QtWidgets.QLabel()
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF;
                background-color: transparent;
                padding: 5px;
            }
        """)
        main_row.addWidget(self.status_label)

        # Spacer pre roztiahnutie
        main_row.addStretch()

        # Purchase button (pravá strana)
        # 🔧 OPRAVA: Použij i18n systém pre purchase button text
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            purchase_text = translator.t("purchase_button")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n purchase button textu: {e}")
            purchase_text = "Zakoupit na rok"  # Fallback

        self.purchase_button = QtWidgets.QPushButton(purchase_text)
        self.purchase_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 12px;
                padding: 6px 12px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.purchase_button.clicked.connect(self.open_purchase_page)
        main_row.addWidget(self.purchase_button)

        layout.addLayout(main_row)

        # Testovací prepínač (len pre vývoj)
        test_row = QtWidgets.QHBoxLayout()
        test_row.addStretch()

        # 🔧 OPRAVA: Použij i18n systém pre demo test button
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            demo_test_text = translator.t("demo_test_button")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n demo test button textu: {e}")
            demo_test_text = "Demo verze"  # Fallback

        self.demo_test_btn = QtWidgets.QPushButton(demo_test_text)
        self.demo_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-size: 10px;
                padding: 4px 8px;
                border: none;
                border-radius: 3px;
            }
        """)
        self.demo_test_btn.clicked.connect(self.test_demo_mode)
        test_row.addWidget(self.demo_test_btn)

        # 🔧 OPRAVA: Použij i18n systém pre full test button
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            full_test_text = translator.t("full_test_button")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n full test button textu: {e}")
            full_test_text = "Plná verze"  # Fallback

        self.full_test_btn = QtWidgets.QPushButton(full_test_text)
        self.full_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-size: 10px;
                padding: 4px 8px;
                border: none;
                border-radius: 3px;
            }
        """)
        self.full_test_btn.clicked.connect(self.test_full_mode)
        test_row.addWidget(self.full_test_btn)

        # Test TTS správy tlačidlo
        # 🔧 OPRAVA: Použij i18n systém pre TTS test button
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            tts_test_text = translator.t("tts_test_button")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n TTS test button textu: {e}")
            tts_test_text = "🔊 Test TTS"  # Fallback

        self.tts_test_btn = QtWidgets.QPushButton(tts_test_text)
        self.tts_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                font-size: 10px;
                padding: 4px 8px;
                border: none;
                border-radius: 3px;
            }
        """)
        self.tts_test_btn.clicked.connect(self.test_demo_expired_tts)
        test_row.addWidget(self.tts_test_btn)

        layout.addLayout(test_row)
    
    def connect_signals(self):
        """Pripojí Qt signály."""
        self.demo_timer.time_updated.connect(self.on_time_updated)
        self.demo_timer.time_expired.connect(self.on_time_expired)
        self.demo_timer.license_changed.connect(self.on_license_changed)

        # 🔧 OPRAVA: Pripoj signály pre thread-safe zobrazenie dialógov
        self.show_demo_expired_dialog.connect(self._show_demo_expired_dialog_safe)
        self.show_error_dialog.connect(self._show_error_dialog_safe)
    
    def update_display(self):
        """Aktualizuje zobrazenie podľa aktuálneho stavu licencie."""
        # Kontrola testovacieho režimu
        if hasattr(self, '_test_mode'):
            if self._test_mode == 'full':
                self._show_full_version_test()
                return
            elif self._test_mode == 'demo':
                self._show_demo_version_test()
                return

        # Normálny režim
        status = self.license_manager.get_license_status()

        if status["type"] == "full":
            self._show_full_version(status)
        else:
            self._show_demo_version(status)

    def _show_full_version(self, status):
        """Zobrazí plnú verziu."""
        days_remaining = status['days_remaining']
        expires_at = status['expires_at']

        # 🔧 OPRAVA: Použij i18n systém pre full version status
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            full_version_text = translator.t("full_version_status").format(
                expires_at=expires_at,
                days_remaining=days_remaining
            )
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n full version status textu: {e}")
            full_version_text = f"🔓 Plná verze, platnost do {expires_at} ({days_remaining} dní)"  # Fallback

        self.status_label.setText(full_version_text)
        self.purchase_button.hide()

    def _show_demo_version(self, status):
        """Zobrazí demo verziu."""
        remaining_minutes = status["remaining_minutes"]
        remaining_seconds = status["remaining_seconds"]

        # Ak beží demo session, odpočítaj aktuálny čas
        if self.license_manager.is_demo_active:
            current_session_time = self.license_manager.get_current_demo_time()
            remaining_seconds = max(0, remaining_seconds - current_session_time)
            remaining_minutes = remaining_seconds // 60

        minutes = remaining_seconds // 60
        seconds = remaining_seconds % 60

        # 🔧 OPRAVA: Použij i18n systém pre GUI text
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            time_str = f"{minutes:02d}:{seconds:02d}"
            demo_text = translator.t("demo_time_remaining").format(time=time_str)
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n GUI textu: {e}")
            # Fallback na hardcoded text
            demo_text = f"🔒 Demo verze, pro dnešek Ti zbývá ještě {minutes:02d}:{seconds:02d}"

        self.status_label.setText(demo_text)
        self.purchase_button.show()

        # Uistíme sa, že widget je viditeľný
        self.show()
        self.setVisible(True)
        self.raise_()
        self.activateWindow()

    def _show_full_version_test(self):
        """Test zobrazenie plnej verzie."""
        # 🔧 OPRAVA: Použij i18n systém pre test full version status
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            full_version_text = translator.t("full_version_status").format(
                expires_at="15.09.2025",
                days_remaining="365"
            )
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n test full version status textu: {e}")
            full_version_text = "🔓 Plná verze, platnost do 15.09.2025 (365 dní)"  # Fallback

        self.status_label.setText(full_version_text)
        self.purchase_button.hide()

    def _show_demo_version_test(self):
        """Test zobrazenie demo verzie."""
        # 🔧 OPRAVA: Použij i18n systém pre test GUI text
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            demo_text = translator.t("demo_time_remaining").format(time="08:45")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n GUI textu: {e}")
            # Fallback na hardcoded text
            demo_text = "🔒 Demo verze, pro dnešek Ti zbývá ještě 08:45"

        self.status_label.setText(demo_text)
        self.purchase_button.show()
    
    def on_time_updated(self, remaining_seconds: int):
        """Callback pre aktualizáciu času."""
        # Ak je testovací režim, neaktualizuj
        if hasattr(self, '_test_mode'):
            return

        if remaining_seconds >= 0:
            minutes = remaining_seconds // 60
            seconds = remaining_seconds % 60

            # 🔧 OPRAVA: Použij i18n systém pre GUI text
            try:
                from i18n_manager import get_translator
                translator = get_translator()
                time_str = f"{minutes:02d}:{seconds:02d}"
                demo_text = translator.t("demo_time_remaining").format(time=time_str)
            except Exception as e:
                logging.warning(f"⚠️ Chyba pri získavaní i18n GUI textu: {e}")
                # Fallback na hardcoded text
                demo_text = f"🔒 Demo verze, pro dnešek Ti zbývá ještě {minutes:02d}:{seconds:02d}"

            self.status_label.setText(demo_text)
        else:
            # Neobmedzený čas (full licencia)
            self.update_display()
    
    def on_time_expired(self):
        """Callback keď demo čas vyprší."""
        # Zastaví čítanie ak beží
        try:
            import common_config as config
            if config.is_reading:
                from app_logic import stop_reading
                stop_reading()
        except Exception as e:
            logging.error(f"Chyba pri zastavovaní čítania: {e}")

        # Prečítaj správu o vypršaní demo času
        self.speak_demo_expired_message()

        # 🔧 OPRAVA: Emituj signál pre thread-safe zobrazenie dialógu
        # Získaj texty pre dialog
        try:
            from i18n_manager import get_translator
            translator = get_translator()
            dialog_title = translator.t("demo_expired_title")
            dialog_text = translator.t("demo_expired_dialog")
        except Exception as e:
            logging.warning(f"⚠️ Chyba pri získavaní i18n demo expired dialog textu: {e}")
            dialog_title = "Demo čas vypršal"  # Fallback
            dialog_text = "Váš denný demo čas vypršal.\n\nPre pokračovanie v používaní si zakúpte plnú verziu na rok.\n\nDemo čas sa obnoví zajtra."

        # Emituj signál pre thread-safe zobrazenie dialógu v main thread
        self.show_demo_expired_dialog.emit(dialog_title, dialog_text)

    @QtCore.pyqtSlot(str, str)
    def _show_demo_expired_dialog_safe(self, dialog_title: str, dialog_text: str):
        """Thread-safe zobrazenie demo expired dialógu v main thread."""
        try:
            QtWidgets.QMessageBox.warning(
                self,
                dialog_title,
                dialog_text
            )
            logging.info("✅ Demo expired dialog zobrazený thread-safe")
        except Exception as e:
            logging.error(f"❌ Chyba pri zobrazovaní demo expired dialógu: {e}")

    @QtCore.pyqtSlot(str, str)
    def _show_error_dialog_safe(self, dialog_title: str, dialog_text: str):
        """Thread-safe zobrazenie error dialógu v main thread."""
        try:
            QtWidgets.QMessageBox.critical(
                self,
                dialog_title,
                dialog_text
            )
            logging.info("✅ Error dialog zobrazený thread-safe")
        except Exception as e:
            logging.error(f"❌ Chyba pri zobrazovaní error dialógu: {e}")

    def speak_demo_expired_message(self):
        """Prečíta správu o vypršaní demo času pomocou TTS."""
        try:
            # Získaj nastavenia z konfigurácie
            import common_config as config

            # Skontroluj, či je TTS pre demo expired povolené
            if not getattr(config, 'DEMO_EXPIRED_TTS_ENABLED', True):
                logging.info("🔇 Demo expired TTS je vypnuté v konfigurácii")
                return

            # 🔧 OPRAVA: Použij i18n systém namiesto hardcoded správy
            # Získaj správu v jazyku čítania (nie aplikácie)
            try:
                from i18n_manager import get_tts_message
                demo_expired_text = get_tts_message("demo_expired_message")
                logging.info(f"🌍 Demo expired správa získaná z i18n: {demo_expired_text}")
            except Exception as e:
                logging.warning(f"⚠️ Chyba pri získavaní i18n správy: {e}")
                # Fallback na konfiguráciu
                demo_expired_text = getattr(config, 'DEMO_EXPIRED_MESSAGE',
                                          "Vypršel limit demo verze pro dnešek. Buď si zakupte plnou verzi, nebo počkejte do zítřka.")

            # 🔧 OPRAVA: Použij hlas pre jazyk čítania namiesto hardcoded 'czech'
            reading_language = getattr(config, 'READING_LANGUAGE', 'cs')
            reading_voice = getattr(config, 'SELECTED_READING_VOICE', None)

            logging.info(f"🔊 Prečítavam správu o vypršaní demo času")
            logging.info(f"   📖 Jazyk čítania: {reading_language}")
            logging.info(f"   🎤 Hlas čítania: {reading_voice}")

            # Pokus o použitie TTS managera s aktuálnymi nastaveniami
            try:
                from tts_manager import speak_text_with_current_settings
                speak_text_with_current_settings(demo_expired_text)
                logging.info("✅ Demo expired správa prečítaná cez TTS manager s aktuálnym hlasom")
                return
            except ImportError:
                logging.warning("⚠️ TTS manager nie je dostupný")
            except Exception as e:
                logging.warning(f"⚠️ TTS manager zlyhal: {e}")

            # Fallback na cross-platform TTS
            try:
                from platform_utils import get_tts_provider
                tts_provider = get_tts_provider()

                # Získaj dostupné hlasy
                voices = tts_provider.get_available_voices()
                if voices:
                    # 🔧 OPRAVA: Pokús sa nájsť hlas pre jazyk čítania
                    preferred_voice = None

                    # Mapovanie jazykov na hľadacie termíny
                    voice_lang_map = {
                        'cs': ['cs', 'czech', 'čeština'],
                        'sk': ['sk', 'slovak', 'slovenčina'],
                        'en': ['en', 'english', 'angličtina'],
                        'de': ['de', 'german', 'nemčina'],
                        'fr': ['fr', 'french', 'francúzština'],
                        'pl': ['pl', 'polish', 'poľština']
                    }

                    # Hľadaj hlas pre jazyk čítania
                    search_terms = voice_lang_map.get(reading_language, ['en'])
                    for voice in voices:
                        voice_lang = voice.get('language', '').lower()
                        voice_name = voice.get('name', '').lower()

                        # Hľadaj podľa jazyka alebo názvu
                        for term in search_terms:
                            if term in voice_lang or term in voice_name:
                                preferred_voice = voice
                                break
                        if preferred_voice:
                            break

                    # Ak nie je nájdený hlas pre jazyk čítania, použij prvý dostupný
                    if not preferred_voice and voices:
                        preferred_voice = voices[0]

                    if preferred_voice:
                        success = tts_provider.speak(demo_expired_text, preferred_voice.get('id'), 200)
                        if success:
                            logging.info(f"✅ Demo expired správa prečítaná cez cross-platform TTS (hlas: {preferred_voice.get('name', 'Unknown')})")
                            return

                logging.warning("⚠️ Žiadne TTS hlasy nie sú dostupné")

            except ImportError:
                logging.warning("⚠️ Cross-platform TTS nie je dostupný")
            except Exception as e:
                logging.warning(f"⚠️ Cross-platform TTS zlyhal: {e}")

            # Posledný fallback - macOS say command
            try:
                import subprocess
                import platform
                if platform.system() == "Darwin":
                    # 🔧 OPRAVA: Použij hlas pre jazyk čítania namiesto hardcoded "Zuzana"
                    # Mapovanie jazykov na macOS hlasy
                    macos_voice_map = {
                        'cs': 'Zuzana',
                        'sk': 'Laura',
                        'en': 'Samantha',
                        'de': 'Anna',
                        'fr': 'Thomas',
                        'pl': 'Zosia'
                    }

                    # Ak máme nastavený konkrétny hlas, pokús sa ho použiť
                    voice_to_use = macos_voice_map.get(reading_language, 'Samantha')
                    if reading_voice:
                        # Extrahuj základný názov hlasu (bez "(Enhanced)" atď.)
                        voice_name = reading_voice.split(' (')[0] if ' (' in reading_voice else reading_voice
                        voice_to_use = voice_name

                    logging.info(f"🔊 Používam macOS hlas: {voice_to_use}")
                    subprocess.run([
                        "say", "-v", voice_to_use, demo_expired_text
                    ], check=False, timeout=10)
                    logging.info("✅ Demo expired správa prečítaná cez macOS say")
                    return
            except Exception as e:
                logging.warning(f"⚠️ macOS say command zlyhal: {e}")

            logging.error("❌ Všetky TTS metódy zlyhali pre demo expired správu")

        except Exception as e:
            logging.error(f"❌ Kritická chyba pri TTS demo expired správe: {e}")

    def on_license_changed(self):
        """Callback keď sa zmení licencia."""
        self.update_display()

    def test_demo_mode(self):
        """Prepne na testovací demo režim."""
        self._test_mode = 'demo'

        # 🔧 OPRAVA: Nastav globálny test flag pre license manager
        self.license_manager._test_mode = 'demo'

        self.update_display()
        logging.info("🧪 Prepnuté na testovací demo režim")

    def test_full_mode(self):
        """Prepne na testovací plný režim."""
        self._test_mode = 'full'

        # 🔧 OPRAVA: Nastav globálny test flag pre license manager
        self.license_manager._test_mode = 'full'

        self.update_display()
        logging.info("🧪 Prepnuté na testovací plný režim")

    def test_demo_expired_tts(self):
        """Test funkcia pre prečítanie demo expired správy."""
        logging.info("🧪 Testovanie demo expired TTS správy")
        self.speak_demo_expired_message()
        logging.info("🧪 Prepnuté na testovací plný režim")

    def reset_test_mode(self):
        """Resetuje testovací režim na normálny."""
        if hasattr(self, '_test_mode'):
            delattr(self, '_test_mode')

        # 🔧 OPRAVA: Resetuj aj globálny test flag pre license manager
        if hasattr(self.license_manager, '_test_mode'):
            delattr(self.license_manager, '_test_mode')

        self.update_display()
        logging.info("🧪 Resetovaný testovací režim")

    def refresh_gui_texts(self):
        """Aktualizuje všetky texty v demo widget podľa aktuálneho jazyka aplikácie."""
        try:
            from i18n_manager import get_translator
            translator = get_translator()

            # Aktualizuj purchase button
            if hasattr(self, 'purchase_button'):
                try:
                    purchase_text = translator.t("purchase_button")
                    self.purchase_button.setText(purchase_text)
                except Exception as e:
                    logging.warning(f"⚠️ Chyba pri aktualizácii purchase button textu: {e}")

            # Aktualizuj test buttons
            if hasattr(self, 'demo_test_btn'):
                try:
                    demo_test_text = translator.t("demo_test_button")
                    self.demo_test_btn.setText(demo_test_text)
                except Exception as e:
                    logging.warning(f"⚠️ Chyba pri aktualizácii demo test button textu: {e}")

            if hasattr(self, 'full_test_btn'):
                try:
                    full_test_text = translator.t("full_test_button")
                    self.full_test_btn.setText(full_test_text)
                except Exception as e:
                    logging.warning(f"⚠️ Chyba pri aktualizácii full test button textu: {e}")

            if hasattr(self, 'tts_test_btn'):
                try:
                    tts_test_text = translator.t("tts_test_button")
                    self.tts_test_btn.setText(tts_test_text)
                except Exception as e:
                    logging.warning(f"⚠️ Chyba pri aktualizácii TTS test button textu: {e}")

            # Aktualizuj status label (ak nie je v test režime)
            if not hasattr(self, '_test_mode'):
                self.update_display()

            logging.info("🌍 Demo widget texty aktualizované podľa jazyka aplikácie")

        except Exception as e:
            logging.error(f"❌ Chyba pri aktualizácii demo widget textov: {e}")

    def open_purchase_page(self):
        """Otvorí stránku pre nákup licencie."""
        try:
            import webbrowser
            purchase_url = self.license_manager.get_purchase_url()
            webbrowser.open(purchase_url)
            logging.info(f"🌐 Otváram nákupnú stránku: {purchase_url}")
        except Exception as e:
            logging.error(f"❌ Chyba pri otváraní nákupnej stránky: {e}")
            # 🔧 OPRAVA: Použij thread-safe error dialog
            self.show_error_dialog.emit(
                "Chyba",
                f"Nepodarilo sa otvoriť nákupnú stránku.\n\nChyba: {e}"
            )


# Globálna inštancia pre jednoduchý prístup
_demo_widget: Optional[DemoWidget] = None

def get_demo_widget(parent=None) -> DemoWidget:
    """Vráti globálnu inštanciu demo widget."""
    global _demo_widget
    if _demo_widget is None:
        _demo_widget = DemoWidget(parent)
    return _demo_widget

def format_time(seconds: int) -> str:
    """Formátuje čas v sekundách na MM:SS formát."""
    if seconds < 0:
        return "∞"
    minutes = seconds // 60
    secs = seconds % 60
    return f"{minutes:02d}:{secs:02d}"
